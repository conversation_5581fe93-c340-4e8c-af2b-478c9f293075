#!/bin/bash
#SBATCH --job-name="trimul"
#SBATCH --output="trimul.out"
#SBATCH --error="trimul.err"
#SBATCH --partition=gpuA100x4
#SBATCH --mem=12G
#SBATCH --nodes=1
#SBATCH --cpus-per-task=1
#SBATCH --constraint="projects"
#SBATCH --gpus-per-node=1
#SBATCH --gpu-bind=closest
#SBATCH --account=bdes-delta-gpu
#SBATCH -t 00:25:00

module reset
module load cuda
module load pytorch

echo -e "job $SLURM_JOBID is starting on `hostname`\n\n"
python3 testing.py